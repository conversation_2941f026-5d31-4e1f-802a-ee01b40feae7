<!--
  *@name feedbackReplyDialog.vue
  *<AUTHOR> Assistant
  *@date 2025/7/29
  *@description 反馈答复Dialog组件
-->
<template>
  <el-dialog
    title="我的反馈"
    :visible.sync="visible"
    width="1500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
    class="feedback-reply-dialog"
  >
    <!-- 搜索条件 -->
    <div class="search-form">
      <el-row :gutter="10">
        <el-col :span="4">
          <div class="form-item">
            <el-input
              v-model="queryList.answerAndQuestion"
              placeholder="请输入问题/回复关键字"
              clearable
            ></el-input>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="form-item">
            <el-input
              v-model="queryList.feedbackContent"
              placeholder="请输入反馈问题关键字"
              clearable
            ></el-input>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="form-item">
            <el-select-multiple
                v-model="queryList.feedbackTypeList"
                style="width: 100%"
                placeholder="反馈类型"
                ref="feedbackTypeList"
                clearable
                confirm>
              <el-select-multiple-option
                  v-for="(item,index) in feedbackTypeOptions"
                  :key="index"
                  :label="item.label"
                  :value="item.value">
              </el-select-multiple-option>
            </el-select-multiple>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="form-item">
            <el-date-picker
              v-model="queryList.createTimeList"
              type="datetimerange"
              :picker-options="pickerOptions"
              start-placeholder="首次提问时间"
              end-placeholder="首次提问时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
            ></el-date-picker>
          </div>
        </el-col>
        <el-col :span="4" style="text-align: right;">
          <div class="form-item">
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </div>
        </el-col>
      </el-row>
    </div>
    <!-- 表格 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        height="500"
        @row-click="handleRowClick"
        :row-class-name="getRowClassName"
        v-loading="tableLoading">
        <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
        <el-table-column label="问题&回复" min-width="400">
          <template slot-scope="scope">
            <div class="question-answer-display">
              <!-- 问题部分 -->
              <div class="question-section">
                <el-tooltip
                  :content="scope.row.question || '--'"
                  placement="top"
                  :disabled="!scope.row.question"
                  :visible-arrow="false"
                  effect="light">
                  <div class="question-line">
                    <span class="label">问：</span>
                    <span class="content text-ellipsis-1">{{ scope.row.question || '--' }}</span>
                  </div>
                </el-tooltip>
              </div>
              <!-- 答案部分 -->
              <div class="answer-section">
                <el-tooltip
                  :content="scope.row.answer || '--'"
                  placement="top"
                  :disabled="!scope.row.answer"
                  :visible-arrow="false"
                  effect="light">
                  <div class="answer-line">
                    <span class="label">答：</span>
                    <span class="content text-ellipsis-2">{{ scope.row.answer || '--' }}</span>
                  </div>
                </el-tooltip>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="反馈内容" width="200">
          <template slot-scope="scope">
            <el-tooltip
              :content="scope.row.feedbackContent || '--'"
              placement="top"
              :disabled="!scope.row.feedbackContent || scope.row.feedbackContent === '--'"
              :visible-arrow="false"
              effect="light">
              <div class="text-ellipsis-3">
                {{ scope.row.feedbackContent ? scope.row.feedbackContent : '--' }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="反馈类型" width="100" align="center" :formatter="feedbackTypeFormatter"></el-table-column>
        <el-table-column label="首次提问时间" width="120" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.createTime" class="time-display">
              <div class="date-line">{{ formatDate(scope.row.createTime) }}</div>
              <div class="time-line">{{ formatTime(scope.row.createTime) }}</div>
            </div>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="最新答复" width="180">
          <template slot-scope="scope">
            <div class="new-reply-container">
              <el-tooltip
                :content="scope.row.newReply || '--'"
                placement="top"
                :disabled="!scope.row.newReply || scope.row.newReply === '--'"
                :visible-arrow="false"
                effect="light">
                <div class="text-ellipsis-3">
                  {{ scope.row.newReply ? scope.row.newReply : '--' }}
                </div>
              </el-tooltip>
              <!-- NEW标志 -->
              <div v-if="scope.row.isReplyNew === '1'" class="new-badge"></div>
              <div v-if="scope.row.isReplyNew === '1'" class="new-text">NEW</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="最新答复时间" width="120" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.newReplyTime" class="time-display">
              <div class="date-line">{{ formatDate(scope.row.newReplyTime) }}</div>
              <div class="time-line">{{ formatTime(scope.row.newReplyTime) }}</div>
            </div>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="110" align="center">
          <template slot-scope="scope">
            <div class="action-buttons">
              <div class="action-btn history-btn" @click="handleHistoryReply(scope.row)">
                历史答复
              </div>
              <div
                v-if="scope.row.isReply === '1'"
                class="action-btn continue-btn"
                @click="handleContinueFeedback(scope.row)">
                继续反馈
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <el-pagination
        style="text-align: center;"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryList.startRow"
        :page-sizes="[10, 20, 50, 200]"
        :page-size="queryList.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
    </el-pagination>

    <!-- 对话记录弹出框 -->
    <conversation-dialog
      v-if="conversationDialogVisible"
      :visible.sync="conversationDialogVisible"
      :feedback-data="selectedFeedbackData"
      :dialog-type="conversationDialogType"
      @close="handleConversationDialogClose"
      @submit-success="handleSubmitSuccess"
    />
  </el-dialog>
</template>

<script>
import { _getFeedbackReplyList } from "@/api/chat";
import ConversationDialog from "./conversationDialog.vue";

export default {
  name: "feedbackReplyDialog",
  components: {
    ConversationDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      queryList: {
        answerAndQuestion: '',
        feedbackContent: '',
        feedbackTypeList: [],
        createTimeList: [],
        startRow: 1,
        pageSize: 10
      },
      tableData: [],
      total: 0,
      tableLoading: false,
      feedbackTypeOptions: [{
        value: '1',
        label: '回答有误'
      }, {
        value: '2',
        label: '响应慢'
      }, {
        value: '3',
        label: '案例有误'
      }, {
        value: '4',
        label: '法规有误'
      }, {
        value: '0',
        label: '其它'
      }],
      pickerOptions: {
        shortcuts: [{
          text: '今天',
          onClick (picker) {
            const start = new Date();
            start.setHours(0, 0, 0, 0); // 设置为当天0点
            const end = new Date();
            end.setHours(23, 59, 59, 999); // 设置为当天23:59:59
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      conversationDialogVisible: false,
      selectedFeedbackData: {},
      conversationDialogType: 'history' // 'history' 或 'continue'
    }
  },
  created() {
    this.handleSearch()
  },
  methods: {
    handleSizeChange(val) {
      this.queryList.pageSize = val
      this.queryList.startRow = 1
      this.getTableData();
    },
    handleCurrentChange(val) {
      this.queryList.startRow = val
      this.getTableData();
    },
    handleSearch() {
      this.queryList.startRow = 1;
      this.getTableData();
    },
    handleReset() {
      this.queryList.answerAndQuestion = ''
      this.queryList.feedbackContent= ''
      this.queryList.feedbackTypeList= []
      this.$refs.feedbackTypeList.clear();
      this.queryList.createTimeList= []
      this.queryList.startRow= 1
      this.queryList.pageSize= 10
      this.getTableData();
    },
    getTableData() {
      this.tableLoading = true;
      let param = {
        question: this.queryList.answerAndQuestion,
        answer: this.queryList.answerAndQuestion,
        feedbackContent: this.queryList.feedbackContent,
        feedbackTypeList: this.queryList.feedbackTypeList,
        startCreateTime: this.queryList.createTimeList[0],
        endCreateTime: this.queryList.createTimeList[1],
        startRow: this.queryList.startRow,
        pageSize: this.queryList.pageSize
      }
      _getFeedbackReplyList(param).then(res => {
        if (res.data.success) {
          this.tableData = res.data.result.list || [];
          this.total = res.data.result.total || 0;
        }
      }).catch(error => {
        console.error('获取反馈列表失败:', error);
        this.$message.error('获取数据失败，请重试');
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    feedbackTypeFormatter (row, column, cellValue, index) {
      if (row.feedbackType === '1') {
        return '回答有误';
      } else if (row.feedbackType === '2') {
        return '响应慢';
      } else if (row.feedbackType === '3') {
        return '案例有误';
      } else if (row.feedbackType === '4') {
        return '法规有误';
      } else if (row.feedbackType === '0') {
        return '其他';
      } else {
        return '--';
      }
    },
    formatDate(dateTimeStr) {
      if (!dateTimeStr) return '';
      // 提取年月日部分 (YYYY-MM-DD)
      return dateTimeStr.split(' ')[0];
    },
    formatTime(dateTimeStr) {
      if (!dateTimeStr) return '';
      // 提取时分秒部分 (HH:mm:ss)
      return dateTimeStr.split(' ')[1] || '';
    },
    handleClose() {
      this.$emit('close');
    },
    handleHistoryReply(row) {
      // 处理历史答复逻辑
      this.selectedFeedbackData = row;
      this.conversationDialogType = 'history';
      this.conversationDialogVisible = true;
    },
    handleContinueFeedback(row) {
      // 处理继续反馈逻辑
      this.selectedFeedbackData = row;
      this.conversationDialogType = 'continue';
      this.conversationDialogVisible = true;
    },
    handleConversationDialogClose() {
      this.conversationDialogVisible = false;
      this.selectedFeedbackData = {};
      this.handleSearch()
    },
    handleSendMessage(data) {
      // 处理发送消息的逻辑
      console.log('发送消息:', data);
      // 这里可以调用API发送消息，然后刷新对话记录
      this.$message.success('消息发送成功');
      // 可以在这里调用API保存消息，然后刷新对话列表
    },
    handleRowClick(row, column, event) {
      // 如果点击的是操作列，不触发行点击事件
      if (column && column.label === '操作') {
        return;
      }
      // 整行点击效果同历史答复
      this.handleHistoryReply(row);
    },
    getRowClassName({row, rowIndex}) {
      return 'clickable-row';
    }
  }
}
</script>

<style scoped lang="scss">
.feedback-reply-dialog {
  .search-form {
    margin-bottom: 10px;
  }

  .table-container {
    margin-bottom: 10px;
  }
}

.text-ellipsis-1 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  max-height: 1.4em; /* 1行 * 1.4行高 */
  word-break: break-word;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  max-height: 2.8em; /* 2行 * 1.4行高 */
  word-break: break-word;
}

.text-ellipsis-3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  max-height: 4.2em; /* 3行 * 1.4行高 */
  word-break: break-word;
}

.time-display {
  .date-line {
    font-size: 13px;
    color: #333;
    line-height: 1.2;
    margin-bottom: 2px;
  }

  .time-line {
    font-size: 12px;
    color: #666;
    line-height: 1.2;
  }
}

.question-answer-display {
  text-align: left;

  .question-section {
    margin-bottom: 8px;

    .question-line {
      display: flex;
      align-items: flex-start;

      .label {
        color: #409EFF;
        font-weight: 500;
        margin-right: 4px;
        flex-shrink: 0;
        font-size: 13px;
      }

      .content {
        flex: 1;
        font-size: 13px;
        color: #333;
      }
    }
  }

  .answer-section {
    .answer-line {
      display: flex;
      align-items: flex-start;

      .label {
        color: #67C23A;
        font-weight: 500;
        margin-right: 4px;
        flex-shrink: 0;
        font-size: 13px;
      }

      .content {
        flex: 1;
        font-size: 13px;
        color: #333;
      }
    }
  }
}

.new-reply-container {
  position: relative;

  .new-badge {
    position: absolute;
    top: 0;
    right: 0;
    width: 40px;
    height: 20px;
    background: #ff4757;
    transform: perspective(10px) rotateX(5deg);
    border-radius: 0 0 0 8px;
    z-index: 10;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -8px;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 0 8px 20px 0;
      border-color: transparent #ff4757 transparent transparent;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: -8px;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 0 0 20px 8px;
      border-color: transparent transparent #ff4757 transparent;
    }
  }

  // NEW文字单独定位
  .new-text {
    position: absolute;
    top: 8px;
    right: 12px;
    color: white;
    font-size: 9px;
    font-weight: bold;
    transform: rotate(45deg);
    letter-spacing: 0.5px;
    line-height: 1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    z-index: 12;
    pointer-events: none;
  }
}

.action-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px; /* 增加按钮间距 */

  .action-btn {
    color: #D50212; /* 文字颜色改为红色 */
    font-size: 12px; /* 字号小一点 */
    cursor: pointer; /* 悬浮变手型 */
    transition: color 0.3s ease;
    text-decoration: none;
    background: none;
    border: none;
    outline: none;
    padding: 0;
    text-align: center;

    &:hover {
      color: #B8020F; /* 悬浮时颜色稍深 */
      text-decoration: underline; /* 悬浮时显示下划线 */
    }

    &:active {
      color: #A0010D; /* 点击时颜色更深 */
    }
  }
}

::v-deep .el-dialog__body {
  padding: 10px 20px;
}

// 表格行点击样式
::v-deep .clickable-row {
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f5f7fa !important;
  }

  td {
    transition: background-color 0.2s ease;
  }
}
</style>
