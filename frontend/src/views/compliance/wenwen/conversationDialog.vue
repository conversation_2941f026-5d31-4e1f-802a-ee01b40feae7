<!--
  *@name conversationDialog.vue
  *<AUTHOR> Assistant
  *@date 2025/7/30
  *@description 反馈对话记录弹出框组件
-->
<template>
  <el-dialog
    title="反馈详情"
    :visible.sync="visible"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true"
    @close="handleClose"
    class="conversation-dialog"
  >
    <!-- 头部问答内容 -->
    <div class="header-qa-content">
      <div class="qa-item">
        <div class="qa-label">问题：</div>
        <div class="qa-content">{{ feedbackData.question || '--' }}</div>
      </div>
      <div class="qa-item">
        <div class="qa-label">回答：</div>
        <div class="qa-content">{{ feedbackData.answer || '--' }}</div>
      </div>
    </div>

    <!-- 聊天对话区域 -->
    <div class="chat-container" ref="chatContainer">
      <!-- Loading状态 -->
      <div v-if="chatLoading" class="chat-loading">
        <i class="el-icon-loading"></i>
        <span>加载对话记录中...</span>
      </div>

      <!-- 对话内容 -->
      <div v-else class="chat-messages">
        <div
          v-for="(message, index) in conversationList"
          :key="index"
          :class="['message-item', message.replyFeedbackStatus === '0' ? 'reply-message' : 'feedback-message']"
        >
          <!-- 答复消息（客服回复，左侧） -->
          <div v-if="message.replyFeedbackStatus === '0'" class="message-left">
            <div class="avatar">{{ getAvatarText(message.realName) }}</div>
            <div class="message-content">
              <div class="message-header">
                <span class="message-time">{{ formatDateTime(message.createTime) }}</span>
              </div>
              <div class="message-text">{{ message.content }}</div>
            </div>
          </div>

          <!-- 反馈消息（用户发送，右侧） -->
          <div v-if="message.replyFeedbackStatus === '1'" class="message-right">
            <div class="message-content">
              <div class="message-header">
                <span class="message-time">{{ formatDateTime(message.createTime) }}</span>
              </div>
              <div class="message-text">{{ message.content }}</div>
            </div>
            <div class="avatar">{{ getAvatarText(message.realName) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部输入框（仅在继续反馈模式下显示） -->
    <div v-if="showContinueFeedback" class="chat-input-area">
      <div class="input-container">
        <div class="input-wrapper">
          <el-input
            v-model="newMessage"
            type="textarea"
            :rows="3"
            placeholder="请输入您的反馈内容..."
            maxlength="200"
            show-word-limit
            resize="none"
            class="message-input"
          ></el-input>
          <el-button
            type="primary"
            @click="handleSendMessage"
            :disabled="!newMessage.trim()"
            class="submit-btn"
            size="small"
          >
            提交
          </el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { _getConversationRecordList, _submitContinueFeedback } from "@/api/chat";

export default {
  name: "conversationDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    feedbackData: {
      type: Object,
      default: () => ({})
    },
    dialogType: {
      type: String,
      default: 'history' // 'history' 或 'continue'
    }
  },
  data() {
    return {
      conversationList: [],
      newMessage: '',
      chatLoading: true
    }
  },
  computed: {
    showContinueFeedback() {
      return this.dialogType === 'continue';
    }
  },
  created() {
    this.getConversationData();
  },
  methods: {
    getConversationData() {
      this.chatLoading = true;
      const params = {
        feedbackId: this.feedbackData.id
      };
      _getConversationRecordList(params).then(res => {
        if (res.data.success) {
          this.conversationList = res.data.result || [];
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        }
      }).catch(err => {
        console.error('获取对话记录失败:', err);
        this.$message.error('获取对话记录失败');
      }).finally(() => {
        this.chatLoading = false;
      });
    },
    getAvatarText(realName) {
      if (!realName) return '客服';
      // 取最后两个字符作为头像
      return realName.length >= 2 ? realName.slice(-2) : realName;
    },
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return '';
      // 格式化时间显示为 2025/07/08 12:00:09 格式
      const date = new Date(dateTimeStr);

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
    },
    scrollToBottom() {
      const container = this.$refs.chatContainer;
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    },
    handleSendMessage() {
      if (!this.newMessage.trim()) {
        return;
      }

      // 触发发送消息事件，传递消息内容和反馈数据
      this.$emit('send-message', {
        content: this.newMessage.trim(),
        feedbackData: this.feedbackData
      });

      // 清空输入框
      this.newMessage = '';
    },
    handleClose() {
      // 清空输入框
      this.newMessage = '';
      this.$emit('close');
    }
  }
}
</script>

<style scoped lang="scss">
.conversation-dialog {
  .header-qa-content {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    height: 90px;
    overflow-y: auto;

    .qa-item {
      display: flex;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .qa-label {
        font-weight: 500;
        color: #333;
        min-width: 50px;
        margin-right: 8px;
      }

      .qa-content {
        flex: 1;
        color: #666;
        line-height: 1.4;
        word-break: break-word;
      }
    }
  }

  .chat-container {
    height: 400px;
    overflow-y: auto;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 16px;
    background: #fafafa;

    .chat-loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #666;

      i {
        font-size: 24px;
        margin-bottom: 8px;
      }

      span {
        font-size: 14px;
      }
    }

    .chat-messages {
      .message-item {
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        .message-left {
          display: flex;
          align-items: flex-start;

          .avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: transparent;
            border: 1px solid #87CEEB;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 500;
            margin-right: 12px;
            flex-shrink: 0;
          }

          .message-content {
            flex: 1;
            max-width: calc(100% - 60px);

            .message-header {
              display: flex;
              align-items: center;
              margin-bottom: 4px;

              .sender-name {
                font-size: 12px;
                color: #666;
                margin-right: 8px;
              }

              .message-time {
                font-size: 11px;
                color: #999;
              }
            }

            .message-text {
              background: transparent;
              border: 1px solid #e0e0e0;
              padding: 10px 12px;
              border-radius: 0 12px 12px 12px;
              color: #333;
              line-height: 1.4;
              word-break: break-word;
              display: inline-block;
              max-width: 70%;
            }
          }
        }

        .message-right {
          display: flex;
          align-items: flex-start;
          justify-content: flex-end;

          .message-content {
            flex: 1;
            max-width: calc(100% - 60px);
            text-align: right;

            .message-header {
              display: flex;
              align-items: center;
              justify-content: flex-end;
              margin-bottom: 4px;

              .message-time {
                font-size: 11px;
                color: #999;
                margin-right: 8px;
              }

              .sender-name {
                font-size: 12px;
                color: #666;
              }
            }

            .message-text {
              background: transparent;
              border: 1px solid #e0e0e0;
              color: #333;
              padding: 10px 12px;
              border-radius: 12px 0 12px 12px;
              line-height: 1.4;
              word-break: break-word;
              text-align: left;
              display: inline-block;
              max-width: 70%;
            }
          }

          .avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: #7A9BFE;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 500;
            margin-left: 12px;
            flex-shrink: 0;
          }
        }
      }
    }
  }

  .chat-input-area {
    border-top: 1px solid #e4e7ed;
    padding: 16px 0 0 0;
    margin-top: 16px;

    .input-container {
      .input-wrapper {
        position: relative;

        .submit-btn {
          position: absolute;
          right: 12px;
          bottom: 32px; // 调整位置，避开字数统计
          z-index: 10;
          height: 28px;
          padding: 0 12px;
          font-size: 12px;
          border-radius: 4px;
        }

        // 调整输入框样式，为按钮留出空间
        ::v-deep .el-textarea__inner {
          padding-right: 80px !important;
        }
      }
    }
  }
}

::v-deep .el-dialog__body {
  padding: 20px;
}
</style>
