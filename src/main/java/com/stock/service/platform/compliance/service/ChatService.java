package com.stock.service.platform.compliance.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import com.stock.core.dao.RedisDao;
import com.stock.core.dto.JsonResponse;
import com.stock.core.dto.UserInfo;
import com.stock.core.exception.BaseException;
import com.stock.core.service.BaseService;
import com.stock.core.util.CryptoUtil;
import com.stock.core.util.DateUtil;
import com.stock.core.util.JsonUtil;
import com.stock.service.platform.block.dto.HttpsClientRequestFactory;
import com.stock.service.platform.common.dao.ChatContentMapper;
import com.stock.service.platform.common.dao.ChatFeedbackMapper;
import com.stock.service.platform.common.dao.ChatFeedbackConversationRecordMapper;
import com.stock.service.platform.common.dao.ChatRecordMapper;
import com.stock.service.platform.common.entity.ChatContentWithBLOBs;
import com.stock.service.platform.common.entity.ChatRecord;
import com.stock.service.platform.common.entity.ChatRecordExample;
import com.stock.service.platform.common.service.CommonService;
import com.stock.service.platform.compliance.dao.ChatContentBizMapper;
import com.stock.service.platform.compliance.dao.ChatRecordBizMapper;
import com.stock.service.platform.compliance.dto.*;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Base64Utils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import javax.servlet.http.HttpServletRequest;
import java.time.Duration;
import java.util.*;
import java.util.stream.Stream;

import static com.stock.service.platform.common.constant.LogicConstant.*;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;


@Service
@Slf4j
@RequiredArgsConstructor
public class ChatService extends BaseService {

    @Value("${service.gui.baseUrl}")
    private String wenUrl;


    @Value("${service.gui.clientSecret}")
    private String clientSecret;

    private final CommonService commonService;
    private final RestTemplate restTemplate;
    private final ChatRecordMapper chatRecordMapper;
    private final ChatRecordBizMapper chatRecordBizMapper;
    private final ChatContentMapper chatContentMapper;
    private final ChatContentBizMapper chatContentBizMapper;
    private final ChatFeedbackMapper chatFeedbackMapper;
    private final ChatFeedbackConversationRecordMapper chatFeedbackConversationRecordMapper;
    private final RedisDao redisDao;
    private final RecordsService recordsService;
    private final WebClient webClient;
    @Setter
    private KeywordProcessor keywordProcessor;

    private final ObjectMapper objectMapper = new ObjectMapper();

    private final String modelListKey = "jt_modelList";

    public List<String> containsCompanyCodeOrName(String string) {
        return keywordProcessor.extractKeywords(string);
    }

    public GetMessageResponse getMessage(GetMessageNonFlowRequest request) throws JsonProcessingException {
        QuestionDto questionDto = new QuestionDto();
        questionDto.setQuestion(request.getQuestion());
        questionDto.setOrigin(request.getOrigin());
        questionDto.setAiType(request.getAiType());
        questionDto.setIp(request.getClientIP());

        //      插入问题
        log.info("insert question:{}", questionDto.getQuestion());
        insertContentWithoutInfo(questionDto, request);
        questionDto.setContentId(questionDto.getId());

        String gettoken = this.getAccessToken(request.getUserId());
        String url = wenUrl + "/chatDongWeb/complianceAPI/getMessage" + "?access_token=" + gettoken;
        HttpsClientRequestFactory factory = new HttpsClientRequestFactory();
        factory.setConnectTimeout(60000 * 1000);
        factory.setReadTimeout(5 * 60000 * 1000);
        RestTemplate restT = new RestTemplate(factory);
        ResponseEntity<JsonResponse<GetMessageResponse>> responseEntity = restT
                .exchange(url, HttpMethod.POST, new HttpEntity<>(questionDto), new ParameterizedTypeReference<JsonResponse<GetMessageResponse>>() {});
//        JsonResponse<?> forObject = restTemplate.postForObject(url, request, JsonResponse.class);

        if (responseEntity.getBody() != null && responseEntity.getBody().getResult() != null) {
            GetMessageResponse result = responseEntity.getBody().getResult();
            ChatContentWithBLOBs chatContent = new ChatContentWithBLOBs();
            chatContent.setId(questionDto.getId());
            chatContent.setStatus(STATUS_SUCCESS);
            chatContent.setPrompt(result.getPrompt().get(0));
            chatContent.setTokens(String.valueOf(result.getTokenUsage().getTotalTokenCount()));
            chatContent.setAnswerContent(result.getContent());
            chatContent.setAnswerTime(new Date());
            chatContentMapper.updateByPrimaryKeySelective(chatContent);

            result.setPrompt(null);
            result.setTokenUsage(null);
            return result;
        }
        return null;
    }

    public Flux<Completions> streamMessage(QuestionDto questionDto, HttpServletRequest httpServletRequest) {
        // Redis缓存key，按用户区分
        String userId = getUserInfo().getUserId();
        String codeInRedis = (String) redisDao.getObject(userId);
        if (codeInRedis != null && codeInRedis.equals(questionDto.getLink())) {
            // 如果相同，直接return空流
            return Flux.just(Completions.builder()
                    .error(true)
                    .isEnd(true)
                    .type("link_error")
                    .build());
        } else {
            redisDao.setObject(userId, questionDto.getLink(), 48 * 60 * 60L); // 设置48小时过期
        }
        String ipAddress = recordsService.getIpAddress(httpServletRequest);
        questionDto.setIp(ipAddress);
        //      插入问题
        log.info("insert question:{}", questionDto.getQuestion());
        List<HistoryMessage> historyListByRecordId = this.getHistoryListByRecordId(questionDto);
        insertContent(questionDto);
        log.info("insert question success");
        StringBuffer sb = new StringBuffer();  // 回答
        StringBuffer thinksb = new StringBuffer();  // 思考
        StringBuffer prompt = new StringBuffer();  // 上下文
        StringBuffer tokens = new StringBuffer(); // token

        GetMessageRequest request = new GetMessageRequest();
        request.setAiType(questionDto.getAiType());
        request.setQuestion(questionDto.getQuestion());
        request.setBelongsPlate(questionDto.getBelongsPlate());
        request.setContinuousChat(questionDto.getContinuousChat());
        request.setOrigin(questionDto.getOrigin());
        request.setHistoryMessages(historyListByRecordId);
        request.setContentId(questionDto.getId());
        ChatContentWithBLOBs chatContent = new ChatContentWithBLOBs();
        chatContent.setId(questionDto.getId());

        return webClient.post().uri(wenUrl + "/chatDongWeb/complianceAPI/streamMessage")
                .bodyValue(request)
                .header("Authorization", commonService.getGuiAccessToken())
                .retrieve()
                .bodyToFlux(Completions.class)
                .delayElements(Duration.ofMillis(50))
                .doOnNext(chatCompletions -> {
                    LocalDateTime date = new LocalDateTime();
                    log.info("推送消息时间: {} {}", DateUtil.getDateStr(date.toDate(), DateUtil.YYYY_MM_DD_HH_MM_SS), date.getMillisOfSecond());
                    if (StringUtils.isNotEmpty(chatCompletions.getContent())) {
                        sb.append(chatCompletions.getContent());  // 拼接回答
                    }
                    if (StringUtils.equals("reasoning_content", chatCompletions.getType()) && StringUtils.isNotEmpty(chatCompletions.getThink())) {
                        thinksb.append(chatCompletions.getThink());  // 拼接思考
                    }
                    if (chatCompletions.getIsEnd()){
                        if (!CollectionUtils.isEmpty(chatCompletions.getPrompt())) {
                            for (String c : chatCompletions.getPrompt()) {
                                prompt.append(c).append("\n");
                            }
                        }
                        if (chatCompletions.getTokenUsage() != null) {
                            tokens.append(chatCompletions.getTokenUsage().getTotalTokenCount().toString());
                        }
                    }
                    if (chatCompletions.getIsEnd()){
                        chatContent.setDataOrder(chatCompletions.getDataOrder());
                    }
                }).doOnCancel(() -> {
                    // 更新内容状态
                    chatContent.setStatus(STATUS_STOP);
                    chatContent.setAnswerContent(String.valueOf(sb));
                    chatContent.setCiteContent(String.valueOf(thinksb));
                    chatContentMapper.updateByPrimaryKeySelective(chatContent);
                }).doOnComplete(() -> {
                    redisDao.delObject(userId);
                    //更新内容状态
                    if (sb.length() == 0) {
                        chatContent.setStatus(STATUS_ERROR);//接口报错
                        chatContent.setAnswerContent(String.valueOf(sb));
                        chatContentMapper.updateByPrimaryKeySelective(chatContent);
                        throw new BaseException("error.500500");
                    } else {
                        chatContent.setPrompt(prompt.toString());//上下文
                        chatContent.setTokens(tokens.toString());//tokens
                        chatContent.setStatus(STATUS_SUCCESS);//回答成功
                        if (thinksb.length() != 0) {
                            chatContent.setCiteContent(String.valueOf(thinksb));
                        }
                        chatContent.setAnswerContent(String.valueOf(sb));
                        chatContent.setAnswerTime(new Date());
                        chatContentMapper.updateByPrimaryKeySelective(chatContent);
                    }
                }).doOnError((e) -> {
                    // 更新内容状态
                    chatContent.setStatus(STATUS_ERROR);
                    chatContent.setAnswerContent(String.valueOf(sb));
                    chatContent.setCiteContent(String.valueOf(thinksb));
                    chatContentMapper.updateByPrimaryKeySelective(chatContent);
                    log.error("问答接口报错: {}", e.getMessage(),e);
                });

    }

    private void insertContent(QuestionDto paramMap) {
        ChatContentWithBLOBs chatContent = new ChatContentWithBLOBs();
        chatContent.setQuestionContent(paramMap.getQuestion());
        chatContent.setRecordId(paramMap.getRecordId());
        UserInfo userInfo = getUserInfo();
        chatContent.setUserId(userInfo.getUserId());
        chatContent.setCreateUser(userInfo.getUserId());
        chatContent.setUserName(userInfo.getUsername());
        chatContent.setUpdateUser(userInfo.getUserId());
        chatContent.setCreateTime(new Date());
        chatContent.setQuestionTime(new Date());
        chatContent.setAiType(paramMap.getAiTypeName());
        chatContent.setIp(paramMap.getIp());
        chatContentMapper.insertSelective(chatContent);
        paramMap.setId(chatContent.getId());
    }

    public ChatResponseDto createChat(ChatRecord chatRecord) {
        ChatResponseDto response = new ChatResponseDto();
        if (chatRecord.getChatName().length() > 100) {
            chatRecord.setChatName(chatRecord.getChatName().trim().substring(0, 100));
        }
        UserInfo userInfo = getUserInfo();
        chatRecord.setUserId(userInfo.getUserId());
        chatRecord.setCreateUser(userInfo.getPersonName());
        chatRecord.setUpdateUser(userInfo.getPersonName());
        chatRecord.setStatus(false);
        chatRecord.setCreateTime(new Date());
        chatRecord.setUpdateTime(new Date());
        chatRecordMapper.insert(chatRecord);
        response.setChatId(chatRecord.getId());
        return response;
    }

    public List<ModelDto> getModelList(String userId) {
        String accessToken = this.getAccessToken(userId);
        String url = wenUrl + "/chatDongWeb/complianceAPI/getModelList"+ "?access_token=" + accessToken;
        try {
            ResponseEntity<JsonResponse<List<ModelDto>>> responseEntity = restTemplate
                    .exchange(url, HttpMethod.GET, new HttpEntity<>(null), new ParameterizedTypeReference<JsonResponse<List<ModelDto>>>() {});


            if (responseEntity.getBody() != null && responseEntity.getBody().getResult() != null) {
                return responseEntity.getBody().getResult();
            }
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("Failed to fetch model list from {}. Error: {}", url, Throwables.getStackTraceAsString(e));
            return Collections.emptyList();
        }
    }

    public List<String> getAiAssistantStandardIssues() {
        List<String> redisDaoObject = (List<String>) redisDao.getObject(getUserInfo().getUserId() + "_question_list");
        if (redisDaoObject != null) {
            return redisDaoObject;
        } else {
            Map<String, String> info = this.getTenantInfo();
            String url = wenUrl + "/chatDongWeb/complianceAPI/getQuestionList" + "?access_token=" + info.get("token");
            try {
                ResponseEntity<JsonResponse<List<String>>> responseEntity = restTemplate
                        .exchange(url, HttpMethod.GET, new HttpEntity<>(null), new ParameterizedTypeReference<JsonResponse<List<String>>>() {});

                if (responseEntity.getBody() != null && responseEntity.getBody().getResult() != null) {
                    redisDao.setObject(getUserInfo().getUserId() + "_question_list", responseEntity.getBody().getResult(), 24 * 60 * 60L); // 设置24小时过期
                    return responseEntity.getBody().getResult();
                }
                return Collections.emptyList();
            } catch (Exception e) {
                log.error("Failed to fetch model list from {}. Error: {}", url, Throwables.getStackTraceAsString(e));
                return Collections.emptyList();
            }
        }

    }

    public ChatResponseDto getChatRecordList() {
        ChatResponseDto responseDto = new ChatResponseDto();
        String userId = getUserInfo().getUserId();
        ChatDongFormDto formDto = new ChatDongFormDto();
        formDto.setUserId(userId);
        String companyCode = getUserInfo().getCompanyCode();
        if (StringUtils.isNotEmpty(companyCode)) {
            formDto.setCompanyCode(companyCode);
        }
        List<ChatRecordDto> chatRecordList = chatRecordBizMapper.getChatRecordList(formDto);
        List<ChatRecordDto> chatRecordListToday = new ArrayList<>();
        List<ChatRecordDto> chatRecordList7Days = new ArrayList<>();
        List<ChatRecordDto> chatRecordList30Days = new ArrayList<>();
        List<ChatRecordDto> chatRecordListOld = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date()); // 设置当前时间
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date todayMidnight = calendar.getTime();
        calendar.add(Calendar.DAY_OF_MONTH, -6);
        Date sixDaysAgoMidnight = calendar.getTime();
        calendar.add(Calendar.DAY_OF_MONTH, -29);
        Date twentyNineDaysAgoMidnight = calendar.getTime();
        chatRecordList.forEach(dto -> {
            Date createTime = dto.getCreateTime();
            if (createTime.after(todayMidnight)) {
                chatRecordListToday.add(dto);
            } else if (createTime.after(sixDaysAgoMidnight)) {
                chatRecordList7Days.add(dto);
            } else if (createTime.after(twentyNineDaysAgoMidnight)) {
                chatRecordList30Days.add(dto);
            } else {
                chatRecordListOld.add(dto);
            }
        });
        responseDto.setChatRecordListToday(chatRecordListToday);
        responseDto.setChatRecordList7Days(chatRecordList7Days);
        responseDto.setChatRecordList30Days(chatRecordList30Days);
        responseDto.setChatRecordListOld(chatRecordListOld);
        return responseDto;
    }

    public ChatResponseDto getChatContent(ChatDongFormDto chatDongFormDto) {
        ChatResponseDto response = new ChatResponseDto();
        List<ChatContentDto> list = chatRecordBizMapper.getChatContentList(chatDongFormDto);
        response.setChatContentList(list);
        return response;
    }

    public MaterialResponse getMaterial(String contentId) {
        Map<String, String> info = this.getTenantInfo();
        String url = wenUrl + "/chatDongWeb/complianceAPI/getMaterial/" + contentId + "?access_token=" + info.get("token");;
        try {
            ResponseEntity<JsonResponse<MaterialResponse>> responseEntity = restTemplate
                    .exchange(url, HttpMethod.GET, new HttpEntity<>(null), new ParameterizedTypeReference<JsonResponse<MaterialResponse>>() {});

            if (responseEntity.getBody() != null && responseEntity.getBody().getResult() != null) {
                return responseEntity.getBody().getResult();
            }
            return null;
        } catch (Exception e) {
            log.error("Failed to fetch model list from {}. Error: {}", url, Throwables.getStackTraceAsString(e));
            return null;
        }
    }

    public Boolean editChatName(ChatDongFormDto chatDongFormDto) {
        ChatRecord chatRecord = new ChatRecord();
        if (chatDongFormDto.getChatName().length() > 100) {
            chatRecord.setChatName(chatDongFormDto.getChatName().trim().substring(0, 100));
        } else {
            chatRecord.setChatName(chatDongFormDto.getChatName().trim());
        }
//        chatRecord.setId(chatDongFormDto.getChatId());
        ChatRecordExample  example = new ChatRecordExample();


        if (chatDongFormDto.getAuto()){
            example.createCriteria().andIdEqualTo(chatDongFormDto.getChatId()).andChatNameEqualTo("新对话");
        }else {
            example.createCriteria().andIdEqualTo(chatDongFormDto.getChatId());
        }
        return chatRecordMapper.updateByExampleSelective(chatRecord,example) > 0;
    }

    public ChatResponseDto delete(ChatDongFormDto chatDongFormDto) {
        ChatResponseDto responseDto = new ChatResponseDto();
        String chatId = chatDongFormDto.getChatId();
        if (StringUtils.isNotEmpty(chatId)) {
            //假删除
            ChatRecordDto record = new ChatRecordDto();
            record.setId(chatId);
            record.setStatus(1);
            chatContentBizMapper.updateRecordStatus(record);
        }
        return responseDto;
    }

    public String getFeedbackType(Map<String, String> map) {
        chatContentBizMapper.updateFeedbackType(map);
        return chatContentBizMapper.selectContent(map);
    }

    public Map<String, String> getTenantInfo() {
        String redisDaoObject = (String) redisDao.getObject(getUserInfo().getUserId() + "_token");
        String object = (String) redisDao.getObject(getUserInfo().getUserId() + "_tenant_info");
        if (redisDaoObject != null && object != null) {
            Map<String, String> map = new HashMap<>();
            map.put("token", redisDaoObject);
            map.put("tenant_info", object);
            return map;
        } else {
            Map<String, String> map = new HashMap<>();
            String guiAccessToken = commonService.getGuiAccessToken();
            String tenantInfo = this.getTenantInfoChangeCompanyCode();
            redisDao.setObject(getUserInfo().getUserId() + "_token", guiAccessToken, 60*60*1000); // 设置1小时过期
            redisDao.setObject(getUserInfo().getUserId() + "_tenant_info", tenantInfo, 60*60*1000); // 设置1小时过期
            map.put("token",guiAccessToken);
            map.put("tenant_info", tenantInfo);
            return map;
        }
    }

    /**
     *
     * 生成租户信息的jwt
     *
     * @return
     *
     */
    public String getTenantInfoChangeCompanyCode() {
        Map<String, String> tenantInfo = Maps.newHashMap();
        tenantInfo.put("companyId", "459097900899955027");
        tenantInfo.put("companyName", "中信建投合规问答");
        tenantInfo.put("companyCode", "TH0292");
        tenantInfo.put("userId", getUserInfo().getUserId());
        tenantInfo.put("username", getUserInfo().getUsername());
        tenantInfo.put("identityType", getUserInfo().getIdentityType());
        tenantInfo.put("personName", getUserInfo().getCompanyShortName() + getUserInfo().getPersonName());
        String encrypted = CryptoUtil.encryptCBC(JsonUtil.toJson(tenantInfo), Base64Utils.encodeToString(clientSecret.getBytes()));
        return Base64Utils.encodeToUrlSafeString(Base64Utils.decodeFromString(encrypted));
    }


    public String getAccessToken(String userId) {
        String token;
        String guiAccessToken = (String) redisDao.getObject(userId + "_token");
        if (guiAccessToken != null) {
            token = guiAccessToken;
        } else {
            token = commonService.getGuiAccessToken();
            redisDao.setObject(userId + "_token", guiAccessToken, 60*60*1000); // 设置1小时过期
        }
        return token;
    }

    private void insertContentWithoutInfo(QuestionDto paramMap,GetMessageNonFlowRequest request) throws JsonProcessingException {
        ChatContentWithBLOBs chatContent = new ChatContentWithBLOBs();
        List<ModelDto> modelListRedis = this.getModelListRedis(request.getUserId());
        for (ModelDto model : modelListRedis) {
            if (StringUtils.equals(model.getModelId(),request.getAiType())){
                chatContent.setAiType(model.getModelName());
            }
        }
        chatContent.setQuestionContent(paramMap.getQuestion());
        chatContent.setRecordId("533668241843961043");
        chatContent.setUserId(request.getUserId());
        chatContent.setCreateUser(request.getUserId());
        chatContent.setUserName(request.getPersonName());
        chatContent.setUpdateUser(request.getUserId());
        chatContent.setCreateTime(new Date());
        chatContent.setQuestionTime(new Date());
        chatContent.setIp(paramMap.getIp());
        chatContentMapper.insertSelective(chatContent);
        paramMap.setId(chatContent.getId());
    }

    public List<ModelDto> getModelListRedis(String userId) throws JsonProcessingException {
        String json = (String) redisDao.getObject(modelListKey);
        if (StringUtils.isNotBlank(json)) {
            List<ModelDto> modelDtos = objectMapper.readValue(json, new TypeReference<List<ModelDto>>() {});
            if (CollectionUtils.isNotEmpty(modelDtos)) {
                return modelDtos;
            }
        }
        List<ModelDto> modelList = this.getModelList(userId);
        redisDao.setObject(modelListKey, JsonUtil.toJson(modelList), 20*60*1000); // 设置20分钟过期
        return modelList;
    }
    /**
     * Agent 获取上下文
     *
     * @param questionDto 问题
     * @return 上下文
     */
    private List<HistoryMessage> getHistoryListByRecordId(QuestionDto questionDto) {
        List<HistoryMessage> historyList = new ArrayList<>();
        // 用户没选连续对话
        if (StringUtils.equals("1",questionDto.getContinuousChat())){
            return historyList;
        }
        List<ChatContentDto> contentDtoList = chatContentBizMapper.selectChatContentListByRecordId(questionDto.getRecordId());
        if (isNotEmpty(contentDtoList)) {
            // 切换模型后，可能是客户想要换个模型验证，需要清空上下文重新做意图识别
            if (!StringUtils.equals(questionDto.getAiTypeName(), contentDtoList.get(0).getAiType())) {
                return historyList;
            }
            // 时间间隔过长（两个小时）的问题，不需要引用上文
            if (new Date().getTime() - contentDtoList.get(0).getCreateTime().getTime() > 2 * 60 * 60 * 1000) {
                return historyList;
            }
            for (ChatContentDto item : contentDtoList) {
                HistoryMessage historyMessage = new HistoryMessage();
                historyMessage.setContent(item.getAnswerContent());
                historyMessage.setQuestion(item.getQuestionContent());
                historyList.add(historyMessage);
            }
        }
        return historyList;
    }

    /**
     * 提交反馈
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean submitFeedback(ChatFeedbackDto feedbackDto) {
        UserInfo userInfo = getUserInfo();

        feedbackDto.setIsReply("0");
        feedbackDto.setIsReplyNew("0");
        feedbackDto.setUserName(userInfo.getUsername());
        feedbackDto.setIsFeedbackNew("1");
        feedbackDto.setNewFeedback(feedbackDto.getFeedbackContent());
        feedbackDto.setNewFeedbackTime(new Date());
        feedbackDto.setStatus("1");
        feedbackDto.setCreateTime(new Date());
        feedbackDto.setCreateUser(userInfo.getUserId());
        chatFeedbackMapper.insertSelective(feedbackDto);

        ChatFeedbackConversationRecordDto conversationRecord = new ChatFeedbackConversationRecordDto();
        conversationRecord.setFeedbackId(feedbackDto.getId());
        conversationRecord.setContent(feedbackDto.getFeedbackContent());
        conversationRecord.setRealName(feedbackDto.getFeedbackName());
        conversationRecord.setReplyFeedbackStatus("1");
        conversationRecord.setCreateTime(new Date());
        conversationRecord.setCreateUser(userInfo.getUserId());
        chatFeedbackConversationRecordMapper.insertSelective(conversationRecord);
        return true;
    }

    /**
     * 删除反馈（软删除）
     */
    public boolean deleteFeedback(String chatContentId) {
        chatFeedbackMapper.deleteByChatContentId(chatContentId);
        return true;
    }

    /**
     * 查询反馈答复列表
     */
    public Map<String, Object> getFeedbackReplyList(ChatFeedbackDto feedbackDto) {
        Map<String, Object> result = new HashMap<>();
        feedbackDto.setUserName(getUserInfo().getUsername());
        feedbackDto.setStartRow((feedbackDto.getStartRow() - 1) * feedbackDto.getPageSize());
        List<ChatFeedbackDto> list = chatFeedbackMapper.getFeedbackReplyList(feedbackDto);
        int total = chatFeedbackMapper.getFeedbackReplyCount(feedbackDto);
        result.put("list", list);
        result.put("total", total);
        return result;
    }

    /**
     * 查询反馈对话记录列表
     */
    public List<ChatFeedbackConversationRecordDto> getConversationRecordList(String feedbackId) {
        // 查询对话记录前，先更新isReplyNew状态为已读
        chatFeedbackMapper.updateIsReplyNewById(feedbackId);
        return chatFeedbackConversationRecordMapper.getConversationRecordList(feedbackId);
    }


}
